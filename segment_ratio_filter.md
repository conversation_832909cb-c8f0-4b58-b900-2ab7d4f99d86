# ZigZag 2B模式EA - 线段长度比例过滤功能

## 功能描述

添加了一个额外的过滤条件，要求A点前的线段长度要大于C点前的线段长度至少1.618倍（黄金比例）。这个过滤条件可以提高2B形态的质量，筛选出更有效的交易信号。

## 新增参数

### EnableSegmentFilter
- **类型**: bool
- **默认值**: true
- **说明**: 启用/禁用线段长度过滤功能

### SegmentRatio
- **类型**: double
- **默认值**: 1.618
- **说明**: A点前线段长度与C点前线段长度的最小比例要求

## 过滤逻辑

### 线段定义
- **A点前的线段**: 从A点前一个ZigZag点到A点的价格距离
- **C点前的线段**: 从B点到C点的价格距离

### 过滤条件
```
A点前线段长度 / C点前线段长度 >= SegmentRatio
```

### 示例
假设有以下ZigZag点序列：
- 点0: 1.2000 (低点)
- 点A: 1.2100 (高点) 
- 点B: 1.2050 (低点)
- 点C: 1.2120 (高点)

计算：
- A点前线段长度 = |1.2100 - 1.2000| = 0.0100
- C点前线段长度 = |1.2120 - 1.2050| = 0.0070
- 比例 = 0.0100 / 0.0070 = 1.43

如果SegmentRatio = 1.618，则此形态会被过滤掉（1.43 < 1.618）。

## 技术分析意义

### 黄金比例的应用
- 1.618是著名的黄金比例，在技术分析中被广泛应用
- 符合黄金比例的形态通常具有更强的技术意义
- 可以过滤掉一些弱势的2B形态

### 形态质量提升
- 确保前期趋势足够强劲
- 避免在震荡市场中产生过多无效信号
- 提高信号的可靠性

## 使用建议

### 参数调整
- **保守设置**: SegmentRatio = 2.0 (更严格的过滤)
- **标准设置**: SegmentRatio = 1.618 (黄金比例)
- **宽松设置**: SegmentRatio = 1.3 (较宽松的过滤)

### 市场适应性
- **趋势市场**: 可以使用较高的比例要求
- **震荡市场**: 可以适当降低比例要求
- **高波动市场**: 建议启用此过滤功能

### 调试功能
启用EnableDebug参数可以查看详细的过滤信息：
```
Pattern rejected: Segment ratio filter failed. Before A: 0.0100, Before C: 0.0070, Ratio: 1.430, Required: 1.618
Pattern accepted: Segment ratio OK. Before A: 0.0120, Before C: 0.0060, Ratio: 2.000
```

## 注意事项

1. **数据要求**: 需要至少4个ZigZag点才能进行过滤（A点前需要有一个点）
2. **性能影响**: 增加了计算复杂度，但影响很小
3. **信号数量**: 启用过滤后，检测到的形态数量可能会减少
4. **参数敏感性**: SegmentRatio参数对结果影响较大，需要根据市场特性调整

## 测试建议

1. 先在历史数据上测试不同的SegmentRatio值
2. 观察过滤前后的信号质量差异
3. 根据交易策略的需要调整参数
4. 在实盘前进行充分的回测验证
