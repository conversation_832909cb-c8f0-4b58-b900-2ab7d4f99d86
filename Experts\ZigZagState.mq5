//+------------------------------------------------------------------+
//|                                                  ZigZagState.mq5 |
//|                        Copyright 2023, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Displays market consolidation/expansion state using ZigZag"
#property description "with visual information panel"

//--- 输入参数
input int      ZigZagDepth = 12;       // ZigZag深度
input int      ZigZagDeviation = 5;    // ZigZag偏差
input int      ZigZagBackstep = 3;     // ZigZag回退步长
input color    PanelColor = clrBlack;  // 面板背景颜色
input int      FontSize = 10;          // 字体大小

//--- 全局变量
int            zigzagHandle;
string         infoLabel[6];
bool           firstRun = true;

//+------------------------------------------------------------------+
//| 专家初始化函数                                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 创建ZigZag指标句柄
   zigzagHandle = iCustom(_Symbol, _Period, "Examples\\ZigZag", 
                          ZigZagDepth, ZigZagDeviation, ZigZagBackstep);
   if(zigzagHandle == INVALID_HANDLE)
   {
      Print("Error creating ZigZag handle");
      return(INIT_FAILED);
   }
   
   // 创建信息面板
   CreateInfoPanel();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 面板背景
   ObjectCreate(0, "InfoPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_YDISTANCE, 20);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_XSIZE, 250);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_YSIZE, 150);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_BGCOLOR, PanelColor);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_BORDER_COLOR, clrGray);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, "InfoPanelBG", OBJPROP_BACK, false);
   
   // 标题
   ObjectCreate(0, "Title", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "Title", OBJPROP_XDISTANCE, 15);
   ObjectSetInteger(0, "Title", OBJPROP_YDISTANCE, 25);
   ObjectSetString(0, "Title", OBJPROP_TEXT, "ZigZag Market State Analyzer");
   ObjectSetInteger(0, "Title", OBJPROP_COLOR, clrYellow);
   ObjectSetInteger(0, "Title", OBJPROP_FONTSIZE, FontSize + 2);
   ObjectSetInteger(0, "Title", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   
   // 市场中期状态标签
   CreateLabel("MidTermLabel", 15, 55, "Market Mid-Term State: ", clrLightSkyBlue);
   CreateLabel("MidTermState", 180, 55, "N/A", clrWhite);
   
   // P0状态标签
   CreateLabel("P0Label", 15, 80, "P0 Relative State: ", clrLightSkyBlue);
   CreateLabel("P0State", 180, 80, "N/A", clrWhite);
   
   // 点信息标签
   CreateLabel("PointsInfo", 15, 105, "Points Collected: 0/5", clrSilver);
   CreateLabel("LastUpdate", 15, 130, "Last Update: " + TimeToString(TimeCurrent()), clrSilver);
}

//+------------------------------------------------------------------+
//| 创建文本标签                                                     |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color clr)
{
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, FontSize);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
}

//+------------------------------------------------------------------+
//| 专家逆初始化函数                                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 删除所有对象
   ObjectsDeleteAll(0, -1, OBJ_LABEL);
   ObjectsDeleteAll(0, -1, OBJ_RECTANGLE_LABEL);
}

//+------------------------------------------------------------------+
//| 专家tick函数                                                    |
//+------------------------------------------------------------------+
void OnTick()
{
   // 每10个tick更新一次
   static int tickCounter = 0;
   if(tickCounter++ % 10 != 0) return;
   
   // 获取ZigZag转折点
   double highs[], lows[];
   if(CopyBuffer(zigzagHandle, 0, 0, 100, highs) <= 0 || 
      CopyBuffer(zigzagHandle, 1, 0, 100, lows) <= 0)
   {
      Print("Error copying ZigZag data");
      return;
   }
   
   // 收集最近的5个转折点
   double points[5];
   int pointCount = 0;
   
   for(int i = 0; i < 100 && pointCount < 5; i++)
   {
      if(highs[i] != 0.0)
      {
         points[pointCount] = highs[i];
         pointCount++;
      }
      if(lows[i] != 0.0 && pointCount < 5)
      {
         points[pointCount] = lows[i];
         pointCount++;
      }
   }
   
   // 更新点信息
   ObjectSetString(0, "PointsInfo", OBJPROP_TEXT, "Points Collected: " + 
                   (string)pointCount + "/5");
   
   // 如果有足够数据，分析市场状态
   if(pointCount == 5)
   {
      // 1. 分析市场中期状态（使用5个转折点）
      string midTermState = AnalyzeMidTermState(points);
      ObjectSetString(0, "MidTermState", OBJPROP_TEXT, midTermState);
      
      // 2. 分析P0相对状态
      string p0State = AnalyzeP0State(points);
      ObjectSetString(0, "P0State", OBJPROP_TEXT, p0State);
   }
   else
   {
      ObjectSetString(0, "MidTermState", OBJPROP_TEXT, "N/A");
      ObjectSetString(0, "P0State", OBJPROP_TEXT, "N/A");
   }
   
   // 更新最后更新时间
   ObjectSetString(0, "LastUpdate", OBJPROP_TEXT, 
                  "Last Update: " + TimeToString(TimeCurrent(), TIME_SECONDS));
}

//+------------------------------------------------------------------+
//| 分析市场中期状态（使用5个转折点）                                 |
//+------------------------------------------------------------------+
string AnalyzeMidTermState(double &points[])
{
   // 计算最近两个线段的幅度
   double amplitude1 = MathAbs(points[0] - points[1]); // P0-P1
   double amplitude2 = MathAbs(points[2] - points[3]); // P2-P3
   double amplitude3 = MathAbs(points[3] - points[4]); // P3-P4
   
   // 计算平均历史幅度
   double avgHistorical = (amplitude2 + amplitude3) / 2.0;
   
   // 确定状态
   if(amplitude1 > avgHistorical * 1.15)
      return "Expansion";
   else if(amplitude1 < avgHistorical * 0.85)
      return "Consolidation";
   
   return "Neutral";
}

//+------------------------------------------------------------------+
//| 分析P0相对状态                                                   |
//+------------------------------------------------------------------+
string AnalyzeP0State(double &points[])
{
   // 计算P0-P1线段的幅度
   double currentAmplitude = MathAbs(points[0] - points[1]);
   
   // 计算前一个同方向线段的幅度
   double prevAmplitude = 0;
   int direction = (points[0] > points[1]) ? 1 : -1; // 1=下降, -1=上升
   
   // 寻找前一个同方向线段
   for(int i = 2; i < 4; i++)
   {
      int prevDir = (points[i] > points[i+1]) ? 1 : -1;
      if(prevDir == direction)
      {
         prevAmplitude = MathAbs(points[i] - points[i+1]);
         break;
      }
   }
   
   // 如果没有找到有效值，使用P2-P3作为替代
   if(prevAmplitude == 0)
      prevAmplitude = MathAbs(points[2] - points[3]);
   
   // 确定相对状态
   if(currentAmplitude > prevAmplitude * 1.15)
      return "Expansion";
   else if(currentAmplitude < prevAmplitude * 0.85)
      return "Consolidation";
   
   return "Neutral";
}
//+------------------------------------------------------------------+