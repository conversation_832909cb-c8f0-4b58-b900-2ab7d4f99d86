//+------------------------------------------------------------------+
//|                                        Test_ZigZag_Algorithm.mq5 |
//|                                    Test ZigZag Algorithm Script   |
//|                                         https://www.augment.com |
//+------------------------------------------------------------------+
#property copyright "Augment Agent"
#property version   "1.00"
#property description "Test script for ZigZag algorithm"
#property script_show_inputs

// 测试参数
input int TestDepth = 12;
input int TestDeviation = 5;
input int TestBackstep = 3;

// ZigZag点结构
struct ZigZagPoint
{
   datetime time;
   double price;
   int barIndex;
   bool isHigh;  // true为高点，false为低点
};

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("开始测试ZigZag算法...");
   
   // 测试ZigZag点提取
   ZigZagPoint points[];
   int pointCount = GetZigZagPoints(points, 20);
   
   Print("找到 ", pointCount, " 个ZigZag点:");
   
   for(int i = 0; i < pointCount; i++)
   {
      Print("点 ", i+1, ": ", 
            "时间=", TimeToString(points[i].time),
            " 价格=", DoubleToString(points[i].price, _Digits),
            " 类型=", (points[i].isHigh ? "高点" : "低点"),
            " K线索引=", points[i].barIndex);
   }
   
   // 在图表上绘制测试点
   DrawTestPoints(points, pointCount);
   
   Print("ZigZag算法测试完成！");
}

//+------------------------------------------------------------------+
//| 获取ZigZag点（复制EA中的算法）                                   |
//+------------------------------------------------------------------+
int GetZigZagPoints(ZigZagPoint &points[], int maxPoints)
{
   int dataSize = MathMin(maxPoints * 20, 500);
   double high[], low[], close[];
   datetime timeArray[];
   
   // 获取价格数据
   if(CopyHigh(_Symbol, _Period, 0, dataSize, high) <= 0 ||
      CopyLow(_Symbol, _Period, 0, dataSize, low) <= 0 ||
      CopyClose(_Symbol, _Period, 0, dataSize, close) <= 0 ||
      CopyTime(_Symbol, _Period, 0, dataSize, timeArray) <= 0)
   {
      Print("Failed to copy price data: ", GetLastError());
      return 0;
   }
   
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(timeArray, true);
   
   // 计算ZigZag点
   ArrayResize(points, 0);
   int foundPoints = 0;
   
   // 寻找摆动高点和低点
   for(int i = TestDepth; i < dataSize - TestDepth && foundPoints < maxPoints; i++)
   {
      bool isSwingHigh = true;
      bool isSwingLow = true;
      
      // 检查是否为摆动高点
      for(int j = i - TestDepth; j <= i + TestDepth; j++)
      {
         if(j != i && high[j] >= high[i])
         {
            isSwingHigh = false;
            break;
         }
      }
      
      // 检查是否为摆动低点
      for(int j = i - TestDepth; j <= i + TestDepth; j++)
      {
         if(j != i && low[j] <= low[i])
         {
            isSwingLow = false;
            break;
         }
      }
      
      // 添加摆动点
      if(isSwingHigh || isSwingLow)
      {
         // 检查与前一个点的最小距离
         if(foundPoints == 0 || i - points[foundPoints-1].barIndex >= TestBackstep)
         {
            ZigZagPoint point;
            point.time = timeArray[i];
            point.barIndex = i;
            point.isHigh = isSwingHigh;
            point.price = isSwingHigh ? high[i] : low[i];
            
            // 验证价格偏差
            double deviation = _Point * TestDeviation;
            if(foundPoints == 0 || MathAbs(point.price - points[foundPoints-1].price) >= deviation)
            {
               ArrayResize(points, foundPoints + 1);
               points[foundPoints] = point;
               foundPoints++;
            }
         }
      }
   }
   
   return foundPoints;
}

//+------------------------------------------------------------------+
//| 在图表上绘制测试点                                               |
//+------------------------------------------------------------------+
void DrawTestPoints(ZigZagPoint &points[], int pointCount)
{
   // 清除之前的测试对象
   ObjectsDeleteAll(0, "TEST_ZZ_");
   
   for(int i = 0; i < pointCount; i++)
   {
      string objName = "TEST_ZZ_" + (string)i;
      
      // 创建箭头标记
      ObjectCreate(0, objName, OBJ_ARROW, 0, points[i].time, points[i].price);
      ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, points[i].isHigh ? 233 : 234);
      ObjectSetInteger(0, objName, OBJPROP_COLOR, points[i].isHigh ? clrRed : clrBlue);
      ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
      
      // 添加文本标签
      string labelName = objName + "_Label";
      double offset = points[i].isHigh ? _Point * 200 : -(_Point * 200);
      ObjectCreate(0, labelName, OBJ_TEXT, 0, points[i].time, points[i].price + offset);
      ObjectSetString(0, labelName, OBJPROP_TEXT, (string)(i+1));
      ObjectSetInteger(0, labelName, OBJPROP_COLOR, points[i].isHigh ? clrRed : clrBlue);
      ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
      
      // 连接相邻点
      if(i > 0)
      {
         string lineName = "TEST_ZZ_Line_" + (string)i;
         ObjectCreate(0, lineName, OBJ_TREND, 0, 
                     points[i-1].time, points[i-1].price,
                     points[i].time, points[i].price);
         ObjectSetInteger(0, lineName, OBJPROP_COLOR, clrGray);
         ObjectSetInteger(0, lineName, OBJPROP_STYLE, STYLE_SOLID);
         ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);
         ObjectSetInteger(0, lineName, OBJPROP_BACK, true);
      }
   }
   
   ChartRedraw();
}
